/**
 * 动态样式管理器
 * 基于配置文件动态生成CSS样式，特别是字体相关的样式
 */

(function() {
    'use strict';
    
    // 等待配置文件加载完成
    function waitForConfig(callback) {
        if (window.CONFIG) {
            callback();
        } else {
            setTimeout(() => waitForConfig(callback), 50);
        }
    }
    
    // 生成字体CSS
    function generateFontCSS() {
        if (!window.CONFIG || !window.CONFIG.FONTS) {
            console.warn('配置文件未加载或字体配置不存在');
            return '';
        }
        
        const fonts = window.CONFIG.FONTS;
        let css = '/* 动态生成的字体样式 */\n';
        
        Object.values(fonts).forEach(font => {
            css += `
@font-face {
    font-family: '${font.name}';
    src: url('${font.url}') format('truetype');
    font-weight: normal;
    font-style: normal;
    font-display: swap;
}
`;
        });
        
        return css;
    }
    
    // 生成视频背景CSS
    function generateVideoCSS() {
        if (!window.CONFIG || !window.CONFIG.VIDEOS) {
            return '';
        }
        
        const video = window.CONFIG.VIDEOS.FLOWER_BACKGROUND;
        return `
/* 动态生成的视频背景样式 */
.video-background video source[src*="123-enhanced"] {
    /* 视频源已通过配置管理 */
}
`;
    }
    
    // 应用动态样式
    function applyDynamicStyles() {
        // 检查是否已经应用过
        if (document.getElementById('dynamic-styles')) {
            return;
        }
        
        const styleElement = document.createElement('style');
        styleElement.id = 'dynamic-styles';
        styleElement.type = 'text/css';
        
        let css = '';
        css += generateFontCSS();
        css += generateVideoCSS();
        
        styleElement.textContent = css;
        document.head.appendChild(styleElement);
        
        console.log('✅ 动态样式已应用');
    }
    
    // 更新视频源
    function updateVideoSources() {
        if (!window.CONFIG || !window.CONFIG.VIDEOS) {
            return;
        }
        
        const videoElements = document.querySelectorAll('video source');
        const flowerVideo = window.CONFIG.VIDEOS.FLOWER_BACKGROUND;
        
        videoElements.forEach(source => {
            const src = source.getAttribute('src');
            if (src && src.includes('123-enhanced')) {
                source.setAttribute('src', flowerVideo.encodedUrl);
                console.log('✅ 视频源已更新:', flowerVideo.encodedUrl);
            }
        });
    }
    
    // 更新脚本源
    function updateScriptSources() {
        if (!window.CONFIG || !window.CONFIG.PATHS) {
            return;
        }
        
        const scripts = window.CONFIG.PATHS.SCRIPTS;
        const scriptElements = document.querySelectorAll('script[src]');
        
        scriptElements.forEach(script => {
            const src = script.getAttribute('src');
            if (src) {
                if (src.includes('romantic-quotes.js')) {
                    script.setAttribute('src', scripts.ROMANTIC_QUOTES);
                } else if (src.includes('modern-quotes-data.js')) {
                    script.setAttribute('src', scripts.MODERN_QUOTES_DATA);
                } else if (src.includes('script.js') && !src.includes('config.js')) {
                    script.setAttribute('src', scripts.MAIN_SCRIPT);
                }
            }
        });
    }
    
    // 创建配置状态指示器（开发模式）
    function createConfigIndicator() {
        if (!window.CONFIG || !window.CONFIG.ENV.isDevelopment) {
            return;
        }
        
        const indicator = document.createElement('div');
        indicator.id = 'config-indicator';
        indicator.style.cssText = `
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0, 255, 0, 0.8);
            color: white;
            padding: 5px 10px;
            border-radius: 5px;
            font-size: 12px;
            z-index: 10000;
            font-family: monospace;
        `;
        indicator.textContent = `Config v${window.CONFIG.SITE.VERSION}`;
        document.body.appendChild(indicator);
        
        // 3秒后自动隐藏
        setTimeout(() => {
            indicator.style.opacity = '0';
            indicator.style.transition = 'opacity 1s';
            setTimeout(() => indicator.remove(), 1000);
        }, 3000);
    }
    
    // 主初始化函数
    function initialize() {
        waitForConfig(() => {
            console.log('🎨 初始化动态样式管理器...');
            
            // 应用动态样式
            applyDynamicStyles();
            
            // 更新媒体资源
            updateVideoSources();
            updateScriptSources();
            
            // 开发模式指示器
            createConfigIndicator();
            
            console.log('✨ 动态样式管理器初始化完成');
        });
    }
    
    // 页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initialize);
    } else {
        initialize();
    }
    
    // 导出到全局（用于调试）
    window.DynamicStyles = {
        applyDynamicStyles,
        updateVideoSources,
        updateScriptSources,
        generateFontCSS,
        initialize
    };
    
})();
