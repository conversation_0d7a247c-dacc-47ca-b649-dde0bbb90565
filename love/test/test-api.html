<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API测试</title>

    <!-- 配置文件 - 必须在其他脚本之前加载 -->
    <script src="/config.js"></script>
</head>
<body>
    <h1>API连接测试</h1>
    <button onclick="testAPI()">测试API连接</button>
    <div id="result"></div>

    <script>
        async function testAPI() {
            const resultDiv = document.getElementById('result');
            resultDiv.innerHTML = '测试中...';

            try {
                console.log('Testing API...');
                const apiUrl = CONFIG.UTILS.getApiUrl(CONFIG.API.ENDPOINTS.MESSAGES);
                console.log('API URL:', apiUrl);
                const response = await fetch(apiUrl);
                console.log('Response status:', response.status);
                const data = await response.json();
                console.log('Response data:', data);
                
                resultDiv.innerHTML = `
                    <h3>API测试结果：</h3>
                    <p>状态码: ${response.status}</p>
                    <p>成功: ${data.success}</p>
                    <p>消息数量: ${data.data ? data.data.length : 0}</p>
                    <pre>${JSON.stringify(data, null, 2)}</pre>
                `;
            } catch (error) {
                console.error('API test error:', error);
                resultDiv.innerHTML = `
                    <h3>API测试失败：</h3>
                    <p>错误: ${error.message}</p>
                `;
            }
        }
    </script>
</body>
</html>
