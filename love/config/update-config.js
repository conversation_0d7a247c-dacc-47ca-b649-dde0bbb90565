#!/usr/bin/env node

/**
 * 配置更新工具
 * 用于批量更新所有文件中的路径引用，使其使用统一的配置系统
 * 
 * 使用方法：
 * node update-config.js
 */

const fs = require('fs');
const path = require('path');

// 配置映射表 - 定义需要替换的路径模式
const CONFIG_MAPPINGS = {
    // API 路径映射
    api: {
        patterns: [
            { from: /const API_BASE_URL = ['"`][^'"`]*['"`]/g, to: "const API_BASE_URL = CONFIG.API.BASE_URL" },
            { from: /['"`]\/api\/messages['"`]/g, to: "CONFIG.UTILS.getApiUrl(CONFIG.API.ENDPOINTS.MESSAGES)" },
            { from: /['"`]\/api\/health['"`]/g, to: "CONFIG.UTILS.getApiUrl(CONFIG.API.ENDPOINTS.HEALTH)" },
            { from: /['"`]\/api['"`]/g, to: "CONFIG.API.BASE_URL" }
        ]
    },
    
    // 字体路径映射
    fonts: {
        patterns: [
            { from: /url\(['"`]\/fonts\/Courgette-Regular\.ttf['"`]\)/g, to: "url(CONFIG.FONTS.COURGETTE.url)" },
            { from: /url\(['"`]\/fonts\/GreatVibes-Regular\.ttf['"`]\)/g, to: "url(CONFIG.FONTS.GREAT_VIBES.url)" },
            { from: /url\(['"`]\/fonts\/字小魂勾玉行书\(商用需授权\)\.ttf['"`]\)/g, to: "url(CONFIG.FONTS.ZI_XIAO_HUN_GOU_YU.url)" },
            { from: /url\(['"`]\/fonts\/字小魂三分行楷\(商用需授权\)\.ttf['"`]\)/g, to: "url(CONFIG.FONTS.ZI_XIAO_HUN_SAN_FEN.url)" },
            { from: /url\(['"`]\/fonts\/字魂行云飞白体\(商用需授权\)\.ttf['"`]\)/g, to: "url(CONFIG.FONTS.ZI_HUN_XING_YUN_FEI_BAI.url)" }
        ]
    },
    
    // 脚本路径映射
    scripts: {
        patterns: [
            { from: /['"`]\/romantic-quotes\.js[^'"`]*['"`]/g, to: "CONFIG.PATHS.SCRIPTS.ROMANTIC_QUOTES" },
            { from: /['"`]\/modern-quotes-data\.js[^'"`]*['"`]/g, to: "CONFIG.PATHS.SCRIPTS.MODERN_QUOTES_DATA" },
            { from: /['"`]\/script\.js[^'"`]*['"`]/g, to: "CONFIG.PATHS.SCRIPTS.MAIN_SCRIPT" }
        ]
    },
    
    // 视频背景路径映射
    videos: {
        patterns: [
            { from: /['"`]\/background\/%E8%8A%B1\/123-enhanced-%5Bproblembo\.com%5D\.mp4['"`]/g, to: "CONFIG.VIDEOS.FLOWER_BACKGROUND.encodedUrl" }
        ]
    }
};

// 需要更新的文件列表
const FILES_TO_UPDATE = [
    'script.js',
    'style.css',
    'html/index.html',
    'html/together-days.html',
    'test/test-api.html',
    'test/test-together-days-api.html',
    'test/test-star-quotes.html',
    'test-modern-quotes.html'
];

/**
 * 读取文件内容
 */
function readFile(filePath) {
    try {
        return fs.readFileSync(filePath, 'utf8');
    } catch (error) {
        console.error(`❌ 无法读取文件 ${filePath}:`, error.message);
        return null;
    }
}

/**
 * 写入文件内容
 */
function writeFile(filePath, content) {
    try {
        fs.writeFileSync(filePath, content, 'utf8');
        return true;
    } catch (error) {
        console.error(`❌ 无法写入文件 ${filePath}:`, error.message);
        return false;
    }
}

/**
 * 应用配置映射到文件内容
 */
function applyConfigMappings(content, filePath) {
    let updatedContent = content;
    let changeCount = 0;
    
    // 根据文件类型应用不同的映射
    const fileExt = path.extname(filePath);
    const isHTML = fileExt === '.html';
    const isCSS = fileExt === '.css';
    const isJS = fileExt === '.js';
    
    Object.entries(CONFIG_MAPPINGS).forEach(([category, config]) => {
        config.patterns.forEach(pattern => {
            const matches = updatedContent.match(pattern.from);
            if (matches) {
                // 对于HTML文件，需要特殊处理
                if (isHTML && category === 'scripts') {
                    // HTML中的脚本引用需要保持字符串形式
                    const replacement = pattern.to.replace('CONFIG.', '${CONFIG.');
                    updatedContent = updatedContent.replace(pattern.from, `"\${${pattern.to}}"`);
                } else if (isCSS && category === 'fonts') {
                    // CSS中的字体引用需要特殊处理
                    updatedContent = updatedContent.replace(pattern.from, `url(\${${pattern.to}})`);
                } else {
                    updatedContent = updatedContent.replace(pattern.from, pattern.to);
                }
                changeCount += matches.length;
                console.log(`  ✅ ${category}: 替换了 ${matches.length} 处引用`);
            }
        });
    });
    
    return { content: updatedContent, changeCount };
}

/**
 * 为HTML文件添加配置文件引用
 */
function addConfigReference(content, filePath) {
    if (!path.extname(filePath) === '.html') return content;
    
    // 检查是否已经包含配置文件引用
    if (content.includes('config.js')) return content;
    
    // 在head标签中添加配置文件引用
    const headMatch = content.match(/<head[^>]*>/i);
    if (headMatch) {
        const insertPosition = content.indexOf(headMatch[0]) + headMatch[0].length;
        const configScript = '\n    <script src="/config.js"></script>';
        return content.slice(0, insertPosition) + configScript + content.slice(insertPosition);
    }
    
    return content;
}

/**
 * 主更新函数
 */
function updateFiles() {
    console.log('🚀 开始更新配置引用...\n');
    
    let totalFiles = 0;
    let updatedFiles = 0;
    let totalChanges = 0;
    
    FILES_TO_UPDATE.forEach(filePath => {
        console.log(`📝 处理文件: ${filePath}`);
        
        const content = readFile(filePath);
        if (!content) return;
        
        totalFiles++;
        
        // 应用配置映射
        const { content: updatedContent, changeCount } = applyConfigMappings(content, filePath);
        
        // 为HTML文件添加配置引用
        const finalContent = addConfigReference(updatedContent, filePath);
        
        if (changeCount > 0 || finalContent !== content) {
            if (writeFile(filePath, finalContent)) {
                updatedFiles++;
                totalChanges += changeCount;
                console.log(`  ✅ 已更新 (${changeCount} 处更改)\n`);
            }
        } else {
            console.log(`  ⏭️  无需更改\n`);
        }
    });
    
    console.log('📊 更新统计:');
    console.log(`  总文件数: ${totalFiles}`);
    console.log(`  已更新文件: ${updatedFiles}`);
    console.log(`  总更改数: ${totalChanges}`);
    console.log('\n✨ 配置更新完成！');
    
    if (updatedFiles > 0) {
        console.log('\n📋 后续步骤:');
        console.log('1. 检查更新后的文件是否正确');
        console.log('2. 重启服务器以应用更改');
        console.log('3. 测试网站功能是否正常');
    }
}

// 执行更新
if (require.main === module) {
    updateFiles();
}

module.exports = { updateFiles, CONFIG_MAPPINGS };
