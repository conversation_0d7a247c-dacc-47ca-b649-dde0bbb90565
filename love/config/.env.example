# Love Website Environment Configuration
# 复制此文件为 .env 并根据实际情况修改配置

# ===== 基本配置 =====
NODE_ENV=production
LOVE_PORT=1314
LOVE_HOST=0.0.0.0

# ===== 域名配置 =====
# 生产环境域名（不包含协议）
LOVE_DOMAIN_PRODUCTION=love.yuh.cool

# 开发环境域名（不包含协议和端口）
LOVE_DOMAIN_DEVELOPMENT=localhost

# ===== 数据库配置 =====
# 数据库文件路径（相对于项目根目录）
LOVE_DB_PATH=./data/love_messages.db

# 备份目录路径（相对于项目根目录）
LOVE_BACKUP_DIR=./data/backups

# ===== 日志配置 =====
# 日志目录路径（相对于项目根目录）
LOVE_LOG_DIR=./logs

# 日志文件名
LOVE_LOG_FILE=backend.log

# ===== 安全配置 =====
# 请求体大小限制
LOVE_BODY_LIMIT=10mb

# 文件上传大小限制
LOVE_UPLOAD_LIMIT=50mb

# ===== API配置 =====
# API基础路径
LOVE_API_BASE_PATH=/api

# 分页默认大小
LOVE_DEFAULT_PAGE_SIZE=20

# 分页最大大小
LOVE_MAX_PAGE_SIZE=100

# ===== 限流配置 =====
# 限流时间窗口（毫秒）
LOVE_RATE_LIMIT_WINDOW=900000

# 限流最大请求数
LOVE_RATE_LIMIT_MAX=100

# ===== 数据库备份配置 =====
# 备份间隔（毫秒）
LOVE_BACKUP_INTERVAL=86400000

# 最大备份数量
LOVE_MAX_BACKUPS=30

# 是否启用备份压缩
LOVE_BACKUP_COMPRESSION=true

# ===== 开发配置 =====
# 是否启用详细日志
LOVE_VERBOSE_LOGGING=false

# 是否启用调试模式
LOVE_DEBUG_MODE=false

# ===== SSL配置（生产环境） =====
# SSL证书路径（如果使用自定义证书）
# LOVE_SSL_CERT_PATH=/path/to/cert.pem
# LOVE_SSL_KEY_PATH=/path/to/key.pem

# ===== 宝塔面板配置 =====
# 宝塔Nginx配置文件路径
BT_NGINX_CONF=/www/server/panel/vhost/nginx/love.yuh.cool.conf

# 宝塔SSL证书目录
BT_SSL_DIR=/www/server/panel/vhost/cert/love.yuh.cool

# ===== 备用Nginx配置 =====
# 系统Nginx配置文件路径
NGINX_CONF=/etc/nginx/sites-available/love.yuh.cool.conf
NGINX_ENABLED=/etc/nginx/sites-enabled/love.yuh.cool.conf
