# 💕 Love Website 配置使用示例

## 🎯 快速开始

### 1. 修改域名配置

如果你需要更改域名（比如从 `love.yuh.cool` 改为 `mylove.example.com`），只需要：

```bash
# 使用配置管理器
./config-manager.sh

# 选择 "1) 创建环境配置文件"
# 输入新的域名: mylove.example.com
```

或者直接编辑 `.env` 文件：
```bash
# 修改这一行
LOVE_DOMAIN_PRODUCTION=mylove.example.com
```

### 2. 更改端口配置

```bash
# 编辑 .env 文件
LOVE_PORT=8080

# 或使用配置管理器
./config-manager.sh
```

## 📝 前端使用示例

### 在HTML中使用配置

```html
<!DOCTYPE html>
<html>
<head>
    <!-- 必须先加载配置文件 -->
    <script src="/config.js"></script>
</head>
<body>
    <script>
        // 获取当前域名
        console.log('当前域名:', CONFIG.DOMAIN.current);
        
        // 获取完整的页面URL
        const pageUrl = CONFIG.UTILS.getPageUrl('/together-days');
        console.log('页面URL:', pageUrl);
        
        // 获取API URL
        const apiUrl = CONFIG.UTILS.getFullApiUrl('/messages');
        console.log('API URL:', apiUrl);
    </script>
</body>
</html>
```

### 在JavaScript中使用配置

```javascript
// 发送API请求
async function fetchMessages() {
    try {
        // 使用配置中的API URL
        const response = await fetch(CONFIG.UTILS.getApiUrl(CONFIG.API.ENDPOINTS.MESSAGES));
        const data = await response.json();
        return data;
    } catch (error) {
        console.error('API请求失败:', error);
    }
}

// 动态设置页面链接
function setupNavigation() {
    const links = {
        home: CONFIG.UTILS.getPageUrl('/'),
        togetherDays: CONFIG.UTILS.getPageUrl('/together-days'),
        anniversary: CONFIG.UTILS.getPageUrl('/anniversary')
    };
    
    // 更新导航链接
    document.querySelectorAll('[data-nav]').forEach(link => {
        const page = link.dataset.nav;
        if (links[page]) {
            link.href = links[page];
        }
    });
}

// 检查当前环境
if (CONFIG.ENV.isDevelopment) {
    console.log('开发环境 - 启用调试模式');
} else {
    console.log('生产环境 - 性能优化模式');
}
```

## 🖥️ 服务器端使用示例

### 在Node.js中使用配置

```javascript
const config = require('./server-config.js');

// 获取数据库路径
const dbPath = config.UTILS.getDatabasePath();
console.log('数据库路径:', dbPath);

// 获取当前域名
console.log('当前域名:', config.DOMAIN.current);
console.log('完整URL:', config.DOMAIN.baseUrl);

// 检查环境
if (config.ENV.isDevelopment) {
    console.log('开发环境配置');
} else {
    console.log('生产环境配置');
}

// 使用CORS配置
app.use(cors(config.SECURITY.CORS));

// 使用API配置
app.get(config.UTILS.getApiUrl('/health'), (req, res) => {
    res.json({ status: 'ok', domain: config.DOMAIN.current });
});
```

### 在Shell脚本中使用配置

```bash
#!/bin/bash

# 加载环境变量
source .env

# 使用配置变量
echo "当前域名: $LOVE_DOMAIN_PRODUCTION"
echo "服务端口: $LOVE_PORT"

# 检查服务状态
curl -s "http://localhost:$LOVE_PORT/api/health"

# 测试外部访问
curl -s "https://$LOVE_DOMAIN_PRODUCTION/api/health"
```

## 🔧 高级配置示例

### 多环境配置

创建不同环境的配置文件：

```bash
# 开发环境
cp .env.example .env.development
# 编辑开发环境配置
LOVE_DOMAIN_PRODUCTION=localhost
NODE_ENV=development

# 生产环境
cp .env.example .env.production  
# 编辑生产环境配置
LOVE_DOMAIN_PRODUCTION=love.yuh.cool
NODE_ENV=production

# 测试环境
cp .env.example .env.testing
# 编辑测试环境配置
LOVE_DOMAIN_PRODUCTION=test.love.yuh.cool
NODE_ENV=testing
```

### 动态配置加载

```javascript
// 根据环境加载不同配置
const env = process.env.NODE_ENV || 'production';
const envFile = `.env.${env}`;

if (require('fs').existsSync(envFile)) {
    require('dotenv').config({ path: envFile });
} else {
    require('dotenv').config({ path: '.env' });
}

const config = require('./server-config.js');
```

## 🚀 部署配置

### 宝塔面板部署

```bash
# 1. 设置生产环境配置
echo "NODE_ENV=production" > .env
echo "LOVE_DOMAIN_PRODUCTION=love.yuh.cool" >> .env
echo "LOVE_PORT=1314" >> .env

# 2. 验证配置
./config-manager.sh
# 选择 "3) 验证配置"

# 3. 启动服务
./manage.sh
# 选择启动服务
```

### Docker部署

```dockerfile
# Dockerfile
FROM node:16-alpine

WORKDIR /app
COPY . .

# 安装依赖
RUN npm install

# 设置环境变量
ENV NODE_ENV=production
ENV LOVE_PORT=1314
ENV LOVE_DOMAIN_PRODUCTION=love.yuh.cool

EXPOSE 1314

CMD ["node", "server.js"]
```

```bash
# 构建和运行
docker build -t love-website .
docker run -d -p 1314:1314 \
  -e LOVE_DOMAIN_PRODUCTION=love.yuh.cool \
  love-website
```

## 🔍 故障排除

### 配置验证

```bash
# 验证所有配置
./config-manager.sh
# 选择 "3) 验证配置"

# 手动检查配置语法
node -c config.js
node -c server-config.js

# 测试配置加载
node -e "console.log(require('./server-config.js').DOMAIN.current)"
```

### 常见问题

1. **域名无法访问**
   - 检查 `.env` 文件中的域名配置
   - 确认DNS解析正确
   - 检查防火墙和端口设置

2. **API请求失败**
   - 验证API端点配置
   - 检查CORS设置
   - 确认服务器运行状态

3. **静态资源加载失败**
   - 检查路径配置
   - 确认文件权限
   - 验证Nginx配置

## 📚 更多信息

- 查看 `CONFIG-README.md` 了解详细配置说明
- 使用 `./config-manager.sh` 进行图形化配置管理
- 查看 `server-config.js` 了解所有可用配置选项
