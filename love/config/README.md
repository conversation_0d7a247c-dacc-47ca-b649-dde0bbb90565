# 💕 Love Website 配置系统

## 📁 配置文件结构

```
love/
├── .env                           # 环境变量配置（保留在根目录）
├── config.js                      # 前端配置文件（保留在根目录）
├── config-manage                  # 配置管理快捷脚本
├── dynamic-styles.js              # 动态样式管理器（保留在根目录）
└── config/                        # 配置文件目录
    ├── .env.example               # 环境变量模板
    ├── CONFIG-README.md           # 详细配置说明
    ├── config-manager.sh          # 配置管理脚本
    ├── config-usage-examples.md   # 使用示例
    ├── manage-config.sh           # 原有配置管理脚本
    ├── server-config.js           # 服务器端配置
    └── update-config.js           # 配置更新工具
```

## 🎯 主要改进

### 1. **文件整理**
- ✅ 配置相关文件统一放在 `config/` 目录
- ✅ `.env` 保留在根目录（标准做法）
- ✅ `config.js` 保留在根目录（前端需要直接访问）
- ✅ 创建 `config-manage` 快捷脚本

### 2. **配置系统完善**
- ✅ 所有HTML文件都引用了 `config.js`
- ✅ 所有JS文件都使用配置而非硬编码
- ✅ 服务器配置路径已修复
- ✅ API路径统一使用配置

### 3. **自动化工具**
- ✅ `config/update-config.js` - 自动更新配置引用
- ✅ `config/config-manager.sh` - 图形化配置管理
- ✅ `config-manage` - 根目录快捷脚本

## 🚀 使用方法

### 快速配置管理
```bash
# 从love根目录运行
./config-manage

# 或者直接运行
./config/config-manager.sh
```

### 修改域名
```bash
# 编辑环境变量
vim .env

# 修改这一行
LOVE_DOMAIN_PRODUCTION=your-new-domain.com

# 验证配置
./config-manage
# 选择 "3) 验证配置"
```

### 更新配置引用
```bash
# 自动更新所有文件中的配置引用
node config/update-config.js
```

## 📋 配置验证清单

### ✅ 已完成项目

1. **文件整理**
   - [x] 配置文件移动到 `config/` 目录
   - [x] 路径引用已更新
   - [x] 快捷脚本已创建

2. **HTML文件配置**
   - [x] `html/index.html` - 已引用config.js
   - [x] `html/together-days.html` - 已引用config.js
   - [x] `html/anniversary.html` - 已引用config.js
   - [x] `html/meetings.html` - 已引用config.js
   - [x] `html/memorial.html` - 已引用config.js

3. **测试文件配置**
   - [x] `test/test-api.html` - 已引用config.js并使用CONFIG
   - [x] `test/test-together-days-api.html` - 已引用config.js并使用CONFIG
   - [x] `test/test-star-quotes.html` - 已更新配置引用

4. **JavaScript文件配置**
   - [x] `script.js` - 已使用CONFIG对象
   - [x] `server.js` - 已使用服务器配置
   - [x] 所有硬编码API路径已替换

5. **服务器配置**
   - [x] 数据库路径已修复
   - [x] 静态文件路径已修复
   - [x] 日志路径已修复

## 🔧 配置项说明

### 环境变量 (.env)
```bash
# 域名配置
LOVE_DOMAIN_PRODUCTION=love.yuh.cool

# 服务器配置
LOVE_PORT=1314
NODE_ENV=production

# 数据库配置
LOVE_DB_PATH=./data/love_messages.db
```

### 前端配置 (config.js)
```javascript
// 域名配置
DOMAIN: {
    PRODUCTION: 'love.yuh.cool',
    get current() { /* 自动检测 */ }
}

// API配置
API: {
    BASE_URL: '/api',
    ENDPOINTS: { /* ... */ }
}
```

### 服务器配置 (config/server-config.js)
```javascript
// 从环境变量读取配置
SERVER: {
    PORT: process.env.LOVE_PORT || 1314,
    DATABASE: {
        PATH: path.join(__dirname, '..', 'data', 'love_messages.db')
    }
}
```

## 🎉 配置系统优势

1. **🔧 统一管理**: 所有配置集中管理，避免分散
2. **🌍 环境适配**: 自动检测开发/生产环境
3. **🛡️ 避免硬编码**: 域名、API等不再硬编码
4. **📱 易于维护**: 修改域名只需要改一个地方
5. **🔍 配置验证**: 自动检查配置完整性
6. **💾 备份恢复**: 配置文件自动备份功能
7. **📁 文件整理**: 配置文件统一存放，目录更整洁

## 🚨 注意事项

1. **路径问题**: 配置文件移动后，所有相对路径都已修复
2. **环境变量**: `.env` 文件保留在根目录，符合标准
3. **前端访问**: `config.js` 保留在根目录，前端可直接访问
4. **快捷脚本**: 使用 `./config-manage` 从根目录管理配置

## 📞 故障排除

如果遇到问题，请按以下步骤检查：

1. **验证配置**
   ```bash
   ./config-manage
   # 选择 "3) 验证配置"
   ```

2. **检查路径**
   ```bash
   node -e "console.log(require('./config/server-config.js').UTILS.getDatabasePath())"
   ```

3. **重新运行更新工具**
   ```bash
   node config/update-config.js
   ```

现在你的Love网站配置系统已经完全整理好了！🎉
