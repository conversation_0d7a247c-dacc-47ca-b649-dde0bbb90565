#!/bin/bash

# New-API 管理脚本使用演示
# 展示交互式菜单和命令行参数两种使用方式

set -e

echo "=== New-API 管理脚本使用演示 ==="

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m'

echo -e "\n${BLUE}🎯 交互式菜单模式${NC}"
echo -e "${GREEN}直接运行脚本即可进入美观的交互式菜单：${NC}"
echo -e "${YELLOW}./manage-new-api.sh${NC}"
echo ""
echo "菜单包含以下功能分类："
echo "📋 服务管理 (1-5): 启动、停止、重启、状态、日志"
echo "🔄 更新管理 (6-7): 更新版本、拉取镜像"
echo "💾 备份管理 (8-11): 备份、恢复、自动备份、列表"
echo "🛠️ 维护管理 (12-14): 清理、监控、重置"
echo "ℹ️ 其他选项 (15-16): 版本信息、帮助文档"

echo -e "\n${BLUE}⚡ 命令行参数模式${NC}"
echo -e "${GREEN}也可以直接使用命令行参数快速执行：${NC}"

echo -e "\n${BLUE}1. 查看服务状态${NC}"
./manage-new-api.sh status

echo -e "\n${BLUE}2. 列出现有备份${NC}"
./manage-new-api.sh list-backups

echo -e "\n${BLUE}3. 查看版本信息${NC}"
./manage-new-api.sh version

echo -e "\n${GREEN}=== 演示完成 ===${NC}"
echo -e "${GREEN}🌐 您的API服务已部署在: https://api.yuh.cool${NC}"
echo -e "${GREEN}📱 现在可以使用以下方式管理 New-API:${NC}"
echo ""
echo -e "${YELLOW}交互式菜单:${NC}"
echo "  ./manage-new-api.sh           # 进入交互式菜单"
echo ""
echo -e "${YELLOW}命令行参数:${NC}"
echo "  ./manage-new-api.sh start     # 启动服务"
echo "  ./manage-new-api.sh stop      # 停止服务"
echo "  ./manage-new-api.sh restart   # 重启服务"
echo "  ./manage-new-api.sh backup    # 备份数据库"
echo "  ./manage-new-api.sh update    # 更新到最新版本"
echo "  ./manage-new-api.sh cleanup   # 清理Docker资源"
