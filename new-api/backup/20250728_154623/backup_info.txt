=== 备份时间 ===
Mon Jul 28 15:46:24 CST 2025

=== Docker 镜像信息 ===
REPOSITORY           TAG       IMAGE ID       CREATED      SIZE
calciumion/new-api   latest    dadf6d9b98c0   2 days ago   190MB

=== 容器状态 ===
NAME      IMAGE                       COMMAND                  SERVICE   CREATED          STATUS                    PORTS
mysql     mysql:8.2                   "docker-entrypoint.s…"   mysql     58 minutes ago   Up 58 minutes             3306/tcp, 33060/tcp
new-api   calciumion/new-api:latest   "/one-api --log-dir …"   new-api   58 minutes ago   Up 58 minutes (healthy)   0.0.0.0:3000->3000/tcp, [::]:3000->3000/tcp
redis     redis:latest                "docker-entrypoint.s…"   redis     58 minutes ago   Up 58 minutes             6379/tcp

=== Git 信息 ===
4d0037a4 fix(topup): update TradeNo field to include varchar type and index for better database performance
f8705489 Merge pull request #1402 from feitianbubu/pr/ali-embedding-support-base64
5b869376 fix: ali embedding support base64
c674c356 fix(response): tools 需要处理的参数很少 使用 map
7aa2972c fix(price): 未设置价格，错误返回模型价格匹配的名字
986558fe fix(db): 修复 db migration 报错过多可能卡住的隐患
a3059597 fix: replace NewError with NewOpenAIError for improved error handling in multiple handlers
d19a6914 fix: create NewOpenAIError function for improved error handling in Relay
4313ede1 fix: set ErrorType to OpenAIError in RelayErrorHandler for better error categorization
952b679c Merge pull request #1352 from wzxjohn/feature/simple_stripe
