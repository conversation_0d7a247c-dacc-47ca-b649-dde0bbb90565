#!/bin/bash

# GPT-Load 综合管理脚本
# 版本: 2.0
# 作者: AI Assistant
# 描述: 用于管理 gpt-load 项目的启动、停止、备份、更新等操作
# 适配宝塔面板环境和反向代理配置

set -e

# 脚本配置
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
SCRIPTS_DIR="${SCRIPT_DIR}/scripts"
PROJECT_NAME="gpt-load"
PROJECT_DIR="$SCRIPT_DIR"
BACKUP_DIR="$PROJECT_DIR/backups"
DATA_DIR="$PROJECT_DIR/data"
WEB_DIR="$PROJECT_DIR/web"
SERVICE_NAME="gpt-load"
LOG_FILE="${SCRIPT_DIR}/data/logs/manage.log"
MAX_BACKUPS=5
DOMAIN="gpt-load.yuh.cool"
PORT=3001

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日志函数
log() {
    local level=$1
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    mkdir -p "$(dirname "${LOG_FILE}")"
    echo -e "${timestamp} [${level}] ${message}" | tee -a "${LOG_FILE}"
}

log_info() {
    log "INFO" "$*"
    printf "\033[0;32m[INFO]\033[0m %s\n" "$*"
}

log_warn() {
    log "WARN" "$*"
    printf "\033[1;33m[WARN]\033[0m %s\n" "$*"
}

log_error() {
    log "ERROR" "$*"
    printf "\033[0;31m[ERROR]\033[0m %s\n" "$*"
}

log_blue() {
    log "INFO" "$*"
    printf "\033[0;34m[INFO]\033[0m %s\n" "$*"
}

# 检查依赖
check_dependencies() {
    local deps=("go" "node" "npm" "git")
    for dep in "${deps[@]}"; do
        if ! command -v "$dep" &> /dev/null; then
            log_error "$dep 未安装，请先安装 $dep"
            exit 1
        fi
    done
    log_info "依赖检查完成"
}

# 创建必要的目录
create_directories() {
    mkdir -p "${SCRIPTS_DIR}" "${BACKUP_DIR}" "$(dirname "${LOG_FILE}")" "${DATA_DIR}/logs"
}

# 显示交互式菜单
show_interactive_menu() {
    clear
    printf "\033[0;34m╔══════════════════════════════════════════════════════════════╗\033[0m\n"
    printf "\033[0;34m║                    GPT-Load 综合管理脚本                       ║\033[0m\n"
    printf "\033[0;34m║                         v2.0                                  ║\033[0m\n"
    printf "\033[0;34m║                   域名: ${DOMAIN}                    ║\033[0m\n"
    printf "\033[0;34m╚══════════════════════════════════════════════════════════════╝\033[0m\n"
    printf "\n"
    printf "\033[0;32m请选择要执行的操作:\033[0m\n"
    printf "\n"
    printf "\033[1;33m📋 服务管理\033[0m\n"
    printf "  \033[0;32m1)\033[0m 启动服务\n"
    printf "  \033[0;32m2)\033[0m 停止服务\n"
    printf "  \033[0;32m3)\033[0m 重启服务\n"
    printf "  \033[0;32m4)\033[0m 查看服务状态\n"
    printf "  \033[0;32m5)\033[0m 查看服务日志\n"
    printf "\n"
    printf "\033[1;33m🔄 更新管理\033[0m\n"
    printf "  \033[0;32m6)\033[0m 更新到最新版本\n"
    printf "  \033[0;32m7)\033[0m 仅拉取最新代码\n"
    printf "  \033[0;32m8)\033[0m 重新构建项目\n"
    printf "\n"
    printf "\033[1;33m💾 备份管理\033[0m\n"
    printf "  \033[0;32m9)\033[0m 手动备份数据\n"
    printf "  \033[0;32m10)\033[0m 恢复数据备份\n"
    printf "  \033[0;32m11)\033[0m 启用自动备份\n"
    printf "  \033[0;32m12)\033[0m 列出所有备份\n"
    printf "\n"
    printf "\033[1;33m🛠️  维护管理\033[0m\n"
    printf "  \033[0;32m13)\033[0m 清理旧备份\n"
    printf "  \033[0;32m14)\033[0m 性能监控\n"
    printf "  \033[0;32m15)\033[0m 重置服务 \033[0;31m(危险操作)\033[0m\n"
    printf "\n"
    printf "\033[1;33mℹ️  其他选项\033[0m\n"
    printf "  \033[0;32m16)\033[0m 显示版本信息\n"
    printf "  \033[0;32m17)\033[0m 显示帮助信息\n"
    printf "  \033[0;32m0)\033[0m 退出\n"
    printf "  \033[0;32mEnter)\033[0m 退出\n"
    printf "\n"
    printf "\033[0;34m═══════════════════════════════════════════════════════════════\033[0m\n"
}

# 显示帮助信息
show_help() {
    cat << EOF
${BLUE}GPT-Load 综合管理脚本${NC}

用法: $0 [选项]

选项:
  ${GREEN}服务管理:${NC}
    start           启动服务
    stop            停止服务
    restart         重启服务
    status          查看服务状态
    logs            查看服务日志

  ${GREEN}更新管理:${NC}
    update          拉取最新代码并重新构建
    pull            仅拉取最新代码
    build           重新构建项目

  ${GREEN}备份管理:${NC}
    backup          手动备份数据
    restore         恢复数据备份
    auto-backup     启用自动备份
    list-backups    列出所有备份

  ${GREEN}维护管理:${NC}
    cleanup         清理旧备份文件
    monitor         性能监控
    reset           重置服务（危险操作）

  ${GREEN}其他:${NC}
    help            显示此帮助信息
    version         显示版本信息

示例:
  $0 start          # 启动服务
  $0 backup         # 备份数据
  $0 update         # 更新到最新版本
  $0 status         # 查看服务状态

${YELLOW}提示: 直接运行 $0 进入交互式菜单${NC}
${YELLOW}域名: ${DOMAIN}${NC}
${YELLOW}端口: ${PORT}${NC}
EOF
}

# 显示版本信息
show_version() {
    printf "\033[0;34mGPT-Load 管理脚本 v2.0\033[0m\n"
    printf "项目路径: %s\n" "${SCRIPT_DIR}"
    printf "域名: %s\n" "${DOMAIN}"
    printf "端口: %s\n" "${PORT}"
    printf "服务名: %s\n" "${SERVICE_NAME}"
}

# 创建备份目录
ensure_backup_dir() {
    if [ ! -d "$BACKUP_DIR" ]; then
        mkdir -p "$BACKUP_DIR"
        log_info "创建备份目录: $BACKUP_DIR"
    fi
}

# 数据备份
backup_data() {
    log_info "开始数据备份..."
    ensure_backup_dir

    local timestamp=$(date +"%Y%m%d_%H%M%S")
    local backup_file="$BACKUP_DIR/gpt-load-backup-$timestamp.tar.gz"

    if [ -d "$DATA_DIR" ]; then
        # 创建完整备份，包含数据和配置
        tar -czf "$backup_file" -C "$PROJECT_DIR" data/ web/.env.production 2>/dev/null || \
        tar -czf "$backup_file" -C "$PROJECT_DIR" data/
        log_info "数据备份完成: $backup_file"

        # 清理旧备份
        cleanup_old_backups
        return 0
    else
        log_warn "数据目录不存在: $DATA_DIR"
        return 1
    fi
}

# 清理旧备份
cleanup_old_backups() {
    log_info "清理旧备份文件..."

    local backup_count=$(ls -1 "$BACKUP_DIR"/gpt-load-backup-*.tar.gz 2>/dev/null | wc -l)

    if [ "$backup_count" -gt "$MAX_BACKUPS" ]; then
        local files_to_delete=$((backup_count - MAX_BACKUPS))
        ls -1t "$BACKUP_DIR"/gpt-load-backup-*.tar.gz | tail -n "$files_to_delete" | xargs rm -f
        log_info "删除了 $files_to_delete 个旧备份文件"
    else
        log_info "当前备份数量: $backup_count，无需清理"
    fi
}

# 列出备份文件
list_backups() {
    log_info "可用的备份文件:"
    if ls -1 "$BACKUP_DIR"/gpt-load-backup-*.tar.gz >/dev/null 2>&1; then
        ls -1t "$BACKUP_DIR"/gpt-load-backup-*.tar.gz | head -10 | nl
    else
        log_warn "没有找到备份文件"
        return 1
    fi
}

# 恢复数据
restore_data() {
    if ! list_backups; then
        return 1
    fi

    echo -n "请输入要恢复的备份文件编号 (1-10) 或输入 'latest' 恢复最新备份: "
    read -r choice

    local backup_file
    if [ "$choice" = "latest" ]; then
        backup_file=$(ls -1t "$BACKUP_DIR"/gpt-load-backup-*.tar.gz 2>/dev/null | head -1)
    else
        backup_file=$(ls -1t "$BACKUP_DIR"/gpt-load-backup-*.tar.gz 2>/dev/null | head -10 | sed -n "${choice}p")
    fi

    if [ -z "$backup_file" ]; then
        log_error "无效的选择"
        return 1
    fi

    log_warn "这将覆盖当前数据，是否继续? (y/N)"
    read -r confirm
    if [[ $confirm =~ ^[Yy]$ ]]; then
        # 停止服务
        stop_service

        # 备份当前数据
        if [ -d "$DATA_DIR" ]; then
            mv "$DATA_DIR" "${DATA_DIR}.backup.$(date +%s)"
            log_info "当前数据已备份"
        fi

        # 恢复数据
        tar -xzf "$backup_file" -C "$PROJECT_DIR"
        log_info "数据恢复完成: $(basename "$backup_file")"

        # 启动服务
        start_service
    else
        log_info "恢复操作已取消"
    fi
}

# 创建宝塔面板反向代理配置说明
create_baota_proxy_guide() {
    local guide_file="${BACKUP_DIR}/baota-proxy-guide.md"

    cat > "$guide_file" << EOF
# GPT-Load 宝塔面板反向代理配置指南

## 域名: ${DOMAIN}
## 后端端口: ${PORT}

### 在宝塔面板中配置反向代理：

1. 登录宝塔面板
2. 进入"网站"管理
3. 找到域名 ${DOMAIN} 或创建新站点
4. 点击"设置" -> "反向代理"
5. 添加反向代理规则：

**代理名称**: GPT-Load
**目标URL**: http://127.0.0.1:${PORT}
**发送域名**: \$host
**内容替换**: 开启

### 内容替换规则（重要）：
- 替换内容: /assets/
- 替换为: /gpt-load/assets/
- 替换内容: "/api/
- 替换为: "/gpt-load/api/
- 替换内容: "/proxy/
- 替换为: "/gpt-load/proxy/

### SSL 配置：
- 确保已为 ${DOMAIN} 配置 SSL 证书
- 开启强制 HTTPS

### 访问地址：
https://${DOMAIN}

### 注意事项：
1. 确保 GPT-Load 服务在端口 ${PORT} 上运行
2. 检查防火墙设置，确保端口 ${PORT} 可访问
3. 如果使用子路径部署，需要相应调整配置

EOF

    log_info "宝塔面板配置指南已生成: $guide_file"
}

# 拉取最新代码
pull_latest_code() {
    log_info "拉取最新代码..."

    # 检查是否是 git 仓库
    if [ ! -d ".git" ]; then
        log_error "当前目录不是 git 仓库"
        return 1
    fi

    # 保存当前分支
    local current_branch=$(git branch --show-current)
    log_info "当前分支: $current_branch"

    # 检查是否有未提交的更改
    if ! git diff --quiet || ! git diff --cached --quiet; then
        log_warn "检测到未提交的更改，将暂存..."
        git stash push -m "Auto stash before update $(date)"
    fi

    # 拉取最新代码
    log_info "从远程仓库拉取最新代码..."
    if git fetch origin; then
        if git pull origin "$current_branch"; then
            log_info "代码拉取成功"
            return 0
        else
            log_error "代码拉取失败"
            return 1
        fi
    else
        log_error "无法连接到远程仓库"
        return 1
    fi
}

# 更新到最新版本
update_to_latest() {
    log_info "开始更新到最新版本..."

    # 备份数据
    backup_data

    # 停止服务
    stop_service

    # 拉取最新代码
    if ! pull_latest_code; then
        log_error "代码更新失败"
        return 1
    fi

    # 重新构建
    if ! build_project; then
        log_error "项目构建失败"
        return 1
    fi

    # 启动服务
    start_service

    log_info "更新完成！"
    log_info "访问地址: https://${DOMAIN}"
}

# 构建项目
build_project() {
    log_info "开始构建项目..."

    # 检查并设置 Go 环境
    export PATH=$PATH:/usr/local/go/bin
    if ! command -v go &> /dev/null; then
        log_error "Go 未找到，请检查安装"
        return 1
    fi

    # 构建前端
    log_info "构建前端..."
    cd "$WEB_DIR"

    # 检查 package.json 是否存在
    if [ ! -f "package.json" ]; then
        log_error "package.json 不存在"
        return 1
    fi

    # 安装依赖
    log_info "安装前端依赖..."
    npm install --production=false

    # 构建
    log_info "构建前端资源..."
    npm run build

    if [ ! -d "dist" ]; then
        log_error "前端构建失败，dist 目录不存在"
        return 1
    fi

    cd "$PROJECT_DIR"

    # 构建后端
    log_info "构建后端..."

    # 检查 go.mod 是否存在
    if [ ! -f "go.mod" ]; then
        log_error "go.mod 不存在"
        return 1
    fi

    # 整理依赖
    go mod tidy

    # 构建二进制文件
    log_info "编译 Go 程序..."
    go build -ldflags="-s -w" -o gpt-load main.go

    if [ ! -f "gpt-load" ]; then
        log_error "后端构建失败"
        return 1
    fi

    # 设置执行权限
    chmod +x gpt-load

    log_info "项目构建完成"
    log_info "前端资源: ${WEB_DIR}/dist"
    log_info "后端程序: ${PROJECT_DIR}/gpt-load"
}

# 启动服务
start_service() {
    log_info "启动 ${PROJECT_NAME} 服务..."

    # 检查端口是否被占用
    if netstat -tlnp 2>/dev/null | grep -q ":${PORT} "; then
        log_warn "端口 ${PORT} 已被占用"
        local pid=$(netstat -tlnp 2>/dev/null | grep ":${PORT} " | awk '{print $7}' | cut -d'/' -f1)
        if [ -n "$pid" ] && [ "$pid" != "-" ]; then
            log_info "尝试停止占用端口的进程 (PID: $pid)"
            kill -9 "$pid" 2>/dev/null || true
            sleep 2
        fi
    fi

    # 检查服务文件是否存在
    if [ -f "/etc/systemd/system/${SERVICE_NAME}.service" ]; then
        systemctl start "$SERVICE_NAME"
        sleep 3
        if systemctl is-active --quiet "$SERVICE_NAME"; then
            log_info "服务启动成功"
            log_info "访问地址: https://${DOMAIN}"
        else
            log_error "服务启动失败"
            systemctl status "$SERVICE_NAME" --no-pager
            return 1
        fi
    else
        log_warn "系统服务文件不存在，尝试直接启动..."
        cd "$PROJECT_DIR"
        nohup ./gpt-load > "${DATA_DIR}/logs/gpt-load.log" 2>&1 &
        local pid=$!
        echo $pid > "${DATA_DIR}/gpt-load.pid"
        log_info "服务已启动 (PID: $pid)"
        log_info "访问地址: https://${DOMAIN}"
    fi
}

# 停止服务
stop_service() {
    log_info "停止 ${PROJECT_NAME} 服务..."

    if [ -f "/etc/systemd/system/${SERVICE_NAME}.service" ]; then
        systemctl stop "$SERVICE_NAME" 2>/dev/null || true
        log_info "系统服务已停止"
    fi

    # 停止可能的直接启动进程
    if [ -f "${DATA_DIR}/gpt-load.pid" ]; then
        local pid=$(cat "${DATA_DIR}/gpt-load.pid")
        if kill -0 "$pid" 2>/dev/null; then
            kill -TERM "$pid"
            sleep 3
            if kill -0 "$pid" 2>/dev/null; then
                kill -9 "$pid"
            fi
            log_info "进程已停止 (PID: $pid)"
        fi
        rm -f "${DATA_DIR}/gpt-load.pid"
    fi

    # 强制清理端口占用
    local pids=$(netstat -tlnp 2>/dev/null | grep ":${PORT} " | awk '{print $7}' | cut -d'/' -f1 | grep -v -)
    if [ -n "$pids" ]; then
        echo "$pids" | xargs kill -9 2>/dev/null || true
        log_info "清理端口占用进程"
    fi
}

# 重启服务
restart_service() {
    log_info "重启 ${PROJECT_NAME} 服务..."
    stop_service
    sleep 2
    start_service
}

# 查看服务状态
show_service_status() {
    log_info "检查 ${PROJECT_NAME} 服务状态..."

    if [ -f "/etc/systemd/system/${SERVICE_NAME}.service" ]; then
        systemctl status "$SERVICE_NAME" --no-pager
    fi

    # 检查端口状态
    if netstat -tlnp 2>/dev/null | grep -q ":${PORT} "; then
        log_info "端口 ${PORT} 正在监听"
        netstat -tlnp 2>/dev/null | grep ":${PORT} "
    else
        log_warn "端口 ${PORT} 未在监听"
    fi

    # 检查进程状态
    if [ -f "${DATA_DIR}/gpt-load.pid" ]; then
        local pid=$(cat "${DATA_DIR}/gpt-load.pid")
        if kill -0 "$pid" 2>/dev/null; then
            log_info "进程运行中 (PID: $pid)"
        else
            log_warn "PID 文件存在但进程未运行"
        fi
    fi
}

# 查看服务日志
show_service_logs() {
    log_info "显示 ${PROJECT_NAME} 服务日志..."

    if [ -f "/etc/systemd/system/${SERVICE_NAME}.service" ]; then
        journalctl -u "$SERVICE_NAME" -f --no-pager
    elif [ -f "${DATA_DIR}/logs/gpt-load.log" ]; then
        tail -f "${DATA_DIR}/logs/gpt-load.log"
    else
        log_warn "未找到日志文件"
    fi
}

# 性能监控
monitor_performance() {
    log_info "GPT-Load 性能监控"
    printf "\n"

    # 系统资源使用情况
    printf "\033[1;33m=== 系统资源使用情况 ===\033[0m\n"
    printf "CPU 使用率: "
    top -bn1 | grep "Cpu(s)" | awk '{print $2}' | cut -d'%' -f1
    printf "内存使用情况:\n"
    free -h
    printf "磁盘使用情况:\n"
    df -h "$PROJECT_DIR"
    printf "\n"

    # 服务状态
    printf "\033[1;33m=== 服务状态 ===\033[0m\n"
    if netstat -tlnp 2>/dev/null | grep -q ":${PORT} "; then
        printf "✅ 服务正在运行 (端口 ${PORT})\n"
        local pid=$(netstat -tlnp 2>/dev/null | grep ":${PORT} " | awk '{print $7}' | cut -d'/' -f1)
        if [ -n "$pid" ] && [ "$pid" != "-" ]; then
            printf "进程 PID: %s\n" "$pid"
            printf "进程资源使用:\n"
            ps -p "$pid" -o pid,ppid,cmd,%mem,%cpu --no-headers 2>/dev/null || echo "无法获取进程信息"
        fi
    else
        printf "❌ 服务未运行\n"
    fi
    printf "\n"

    # 日志文件大小
    printf "\033[1;33m=== 日志文件状态 ===\033[0m\n"
    if [ -d "${DATA_DIR}/logs" ]; then
        du -sh "${DATA_DIR}/logs"/* 2>/dev/null || echo "无日志文件"
    else
        echo "日志目录不存在"
    fi
    printf "\n"

    # 备份文件状态
    printf "\033[1;33m=== 备份文件状态 ===\033[0m\n"
    if [ -d "$BACKUP_DIR" ]; then
        local backup_count=$(ls -1 "$BACKUP_DIR"/gpt-load-backup-*.tar.gz 2>/dev/null | wc -l)
        printf "备份文件数量: %s\n" "$backup_count"
        if [ "$backup_count" -gt 0 ]; then
            printf "备份总大小: "
            du -sh "$BACKUP_DIR" | cut -f1
            printf "最新备份: "
            ls -1t "$BACKUP_DIR"/gpt-load-backup-*.tar.gz 2>/dev/null | head -1 | xargs basename
        fi
    else
        echo "备份目录不存在"
    fi
}

# 自动备份任务
setup_auto_backup() {
    log_info "设置自动备份任务..."

    local backup_time=${1:-"0 2 * * *"}  # 默认每天凌晨2点
    local cron_job="$backup_time $SCRIPT_DIR/manage-gptload.sh backup >/dev/null 2>&1"

    # 检查是否已存在
    if crontab -l 2>/dev/null | grep -q "$SCRIPT_DIR/manage-gptload.sh backup"; then
        log_warn "自动备份任务已存在，将更新时间设置"
        # 移除旧的任务
        crontab -l 2>/dev/null | grep -v "$SCRIPT_DIR/manage-gptload.sh backup" | crontab -
    fi

    # 添加到 crontab
    (crontab -l 2>/dev/null; echo "$cron_job") | crontab -
    log_info "自动备份任务已设置: $backup_time"
}

# 重置服务（危险操作）
reset_service() {
    log_warn "⚠️  重置服务将删除所有数据和配置！"
    printf "\033[0;31m这是一个危险操作，将会：\033[0m\n"
    printf "1. 停止服务\n"
    printf "2. 删除所有数据文件\n"
    printf "3. 删除所有日志文件\n"
    printf "4. 保留备份文件\n"
    printf "\n"

    printf "\033[1;33m请输入 'RESET' 确认重置操作: \033[0m"
    read -r confirm

    if [ "$confirm" = "RESET" ]; then
        log_warn "开始重置服务..."

        # 停止服务
        stop_service

        # 删除数据目录（保留备份）
        if [ -d "$DATA_DIR" ]; then
            rm -rf "${DATA_DIR:?}"/*
            log_info "数据目录已清空"
        fi

        # 重新创建必要目录
        mkdir -p "${DATA_DIR}/logs"

        log_info "服务重置完成"
        log_info "可以使用 'restore' 命令恢复备份数据"
    else
        log_info "重置操作已取消"
    fi
}

# 处理交互式菜单选择
handle_menu_choice() {
    local choice=$1

    case $choice in
        1)
            log_info "启动 ${PROJECT_NAME} 服务..."
            start_service
            ;;
        2)
            log_info "停止 ${PROJECT_NAME} 服务..."
            stop_service
            ;;
        3)
            log_info "重启 ${PROJECT_NAME} 服务..."
            restart_service
            ;;
        4)
            show_service_status
            ;;
        5)
            show_service_logs
            ;;
        6)
            log_info "更新 ${PROJECT_NAME} 到最新版本..."
            update_to_latest
            ;;
        7)
            log_info "拉取最新代码..."
            pull_latest_code
            ;;
        8)
            log_info "重新构建项目..."
            build_project
            ;;
        9)
            log_info "开始备份数据..."
            backup_data
            ;;
        10)
            log_info "恢复数据备份..."
            restore_data
            ;;
        11)
            log_info "配置自动备份..."
            printf "\033[1;33m当前默认备份时间: 每天凌晨2点\033[0m\n"
            read -p "是否使用默认时间？(y/N): " use_default
            if [[ $use_default =~ ^[Yy]$ ]]; then
                setup_auto_backup
            else
                printf "\033[1;33m请输入cron格式的时间 (例如: '0 3 * * *' 表示每天凌晨3点):\033[0m\n"
                read -p "备份时间: " backup_time
                if [ -n "$backup_time" ]; then
                    setup_auto_backup "$backup_time"
                else
                    log_error "未指定备份时间"
                fi
            fi
            ;;
        12)
            list_backups
            ;;
        13)
            log_info "清理旧备份..."
            cleanup_old_backups
            ;;
        14)
            monitor_performance
            ;;
        15)
            log_warn "重置服务（这将删除所有数据）..."
            reset_service
            ;;
        16)
            show_version
            ;;
        17)
            show_help
            ;;
        0)
            log_info "退出管理脚本"
            exit 0
            ;;
        *)
            log_error "无效选择: $choice"
            return 1
            ;;
    esac
}

# 交互式菜单主循环
interactive_menu() {
    while true; do
        show_interactive_menu

        printf "\033[0;32m请输入您的选择 (0-17 或 Enter 退出): \033[0m"
        read -r choice

        # 处理空输入（Enter键）
        if [ -z "$choice" ]; then
            printf "\033[0;32m[INFO]\033[0m 退出管理脚本\n"
            exit 0
        fi

        # 验证输入是否为数字
        if ! [[ "$choice" =~ ^[0-9]+$ ]]; then
            printf "\033[0;31m错误: 请输入有效的数字\033[0m\n"
            read -p "按回车键继续..." -r
            continue
        fi

        # 处理选择
        printf "\n"
        handle_menu_choice "$choice"

        # 如果不是退出选项，等待用户确认后继续
        if [ "$choice" != "0" ]; then
            printf "\n"
            read -p "按回车键返回主菜单..." -r
        fi
    done
}

# 主函数
main() {
    # 检查依赖和创建目录
    check_dependencies
    create_directories

    # 如果没有参数，启动交互式菜单
    if [ $# -eq 0 ]; then
        interactive_menu
        exit 0
    fi

    local command=$1
    shift

    case $command in
        start)
            log_info "启动 ${PROJECT_NAME} 服务..."
            start_service "$@"
            ;;
        stop)
            log_info "停止 ${PROJECT_NAME} 服务..."
            stop_service "$@"
            ;;
        restart)
            log_info "重启 ${PROJECT_NAME} 服务..."
            restart_service "$@"
            ;;
        status)
            show_service_status "$@"
            ;;
        logs)
            show_service_logs "$@"
            ;;
        update)
            log_info "更新 ${PROJECT_NAME} 到最新版本..."
            update_to_latest "$@"
            ;;
        pull)
            log_info "拉取最新代码..."
            pull_latest_code "$@"
            ;;
        build)
            log_info "重新构建项目..."
            build_project "$@"
            ;;
        backup)
            log_info "开始备份数据..."
            backup_data "$@"
            ;;
        restore)
            log_info "开始恢复数据..."
            restore_data "$@"
            ;;
        auto-backup)
            log_info "配置自动备份..."
            setup_auto_backup "$@"
            ;;
        list-backups)
            list_backups "$@"
            ;;
        cleanup)
            log_info "清理旧备份..."
            cleanup_old_backups "$@"
            ;;
        monitor)
            monitor_performance "$@"
            ;;
        reset)
            log_warn "重置服务（这将删除所有数据）..."
            reset_service "$@"
            ;;
        menu|interactive)
            interactive_menu
            ;;
        help|--help|-h)
            show_help
            ;;
        version|--version|-v)
            show_version
            ;;
        *)
            log_error "未知命令: $command"
            echo ""
            show_help
            exit 1
            ;;
    esac
}

# 运行主函数
main "$@"
